#!/usr/bin/env python3
"""
Tesla Model Y Juniper Monitor - Live Version
"""

import time
import sys
from datetime import datetime
from colorama import init, Fore, Style
from tesla_bot import TeslaInventoryMonitor
import config

# Initialize colorama
init(autoreset=True)

def main():
    print(f"{Fore.CYAN}🚗 Tesla Model Y Juniper Bot - LIVE MONITORING{Style.RESET_ALL}", flush=True)
    print(f"{Fore.YELLOW}Target: Model Y RWD (Juniper) - Max Price: {config.MAX_PRICE:,} {config.CURRENCY}{Style.RESET_ALL}", flush=True)
    print(f"{Fore.BLUE}Checking every {config.CHECK_INTERVAL*1000:.0f}ms (ultra-fast monitoring!){Style.RESET_ALL}", flush=True)
    print(f"{Fore.RED}⚠️  Press Ctrl+C to stop{Style.RESET_ALL}", flush=True)
    print("=" * 60, flush=True)
    
    monitor = TeslaInventoryMonitor()
    check_count = 0
    
    try:
        while True:
            check_count += 1
            
            # Check inventory
            vehicles = monitor.check_inventory()
            
            if vehicles:
                print(f"\n{Fore.GREEN}🎉 VEHICLE(S) FOUND! 🎉{Style.RESET_ALL}", flush=True)
                print("=" * 60, flush=True)
                
                for vehicle in vehicles:
                    monitor.display_vehicle_info(vehicle)
                
                print(f"{Fore.RED}⚠️  VEHICLES AVAILABLE! Check details above!{Style.RESET_ALL}", flush=True)
                print("=" * 60, flush=True)
            
            # Show status every 100 checks (10 seconds at 100ms)
            if check_count % 100 == 0:
                current_time = datetime.now().strftime("%H:%M:%S")
                print(f"{Fore.BLUE}[{current_time}] Check #{check_count:,} - No vehicles found - Monitoring...{Style.RESET_ALL}", flush=True)
            
            # Sleep for the configured interval
            time.sleep(config.CHECK_INTERVAL)
            
    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}🛑 Monitoring stopped by user{Style.RESET_ALL}", flush=True)
        print(f"{Fore.CYAN}Total checks performed: {check_count:,}{Style.RESET_ALL}", flush=True)
        print(f"{Fore.GREEN}Thanks for using Tesla Bot!{Style.RESET_ALL}", flush=True)
    except Exception as e:
        print(f"\n{Fore.RED}❌ Error: {e}{Style.RESET_ALL}", flush=True)
        print(f"{Fore.YELLOW}Check tesla_bot.log for details{Style.RESET_ALL}", flush=True)

if __name__ == "__main__":
    main()

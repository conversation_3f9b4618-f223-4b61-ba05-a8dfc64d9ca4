# Tesla Model Y Juniper Order Bot 🚗

An automated bot for monitoring Tesla Turkey inventory and placing orders for Model Y Juniper (Standard Range RWD) vehicles.

## ⚠️ Important Disclaimers

- **Use at your own risk**: This bot interacts with Tesla's systems and may violate their Terms of Service
- **No guarantees**: The bot may not work due to Tesla's anti-bot measures or system changes
- **Legal compliance**: Ensure you comply with Turkish laws and Tesla's terms
- **Manual CAPTCHA**: You will need to manually solve CAPTCHAs during the ordering process

## 🎯 Features

- **Ultra-fast monitoring**: Continuously checks Tesla Turkey inventory every 100ms (0.1 seconds)
- **Targeted search**: Specifically looks for Model Y Juniper (RWD Standard Range) around 1.9M TL
- **Automatic filtering**: Only shows vehicles matching your criteria
- **Colored output**: Easy-to-read console output with color coding
- **Logging**: Comprehensive logging to file and console
- **Configuration**: Easy configuration through environment variables

## 📋 Requirements

- Python 3.8 or higher
- Windows 10/11 (tested environment)
- Chrome browser (for Selenium automation)
- Stable internet connection

## 🚀 Installation

1. **Clone or download** this project to your local machine

2. **Install Python dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure your information**:
   - Copy `.env.example` to `.env`
   - Fill in your personal and payment information in the `.env` file

4. **Review configuration**:
   - Check `config.py` for any settings you want to modify
   - Default check interval is 30 seconds
   - Maximum price is set to 2M TL

## 🔧 Configuration

### Personal Information (.env file)
```
FIRST_NAME=Your_First_Name
LAST_NAME=Your_Last_Name
EMAIL=<EMAIL>
PHONE=+90XXXXXXXXXX
ADDRESS=Your_Full_Address
CITY=Your_City
POSTAL_CODE=Your_Postal_Code
ID_NUMBER=Your_Turkish_ID_Number
```

### Payment Information (.env file)
```
CARD_NUMBER=1234567890123456
CARD_HOLDER=Your_Name_On_Card
EXPIRY_MONTH=12
EXPIRY_YEAR=25
CVV=123
```

## 🎮 Usage

### Basic Monitoring

**Option 1: Quick Start (Recommended)**
```bash
python run_bot.py
```

**Option 2: Direct Run**
```bash
python tesla_bot.py
```

The bot will:
1. Start monitoring Tesla Turkey inventory
2. Check every 100ms (0.1 seconds) for Model Y Juniper availability - VERY FAST!
3. Display any found vehicles with details immediately
4. Continue running until you stop it (Ctrl+C)

⚠️ **Important**: The 100ms interval is very aggressive. If you get rate-limited, the bot will automatically slow down.

### Understanding the Output

- **Blue text**: General information and status updates
- **Green text**: Vehicle found alerts and positive information
- **Yellow text**: Warnings and important notices
- **Red text**: Errors or critical alerts
- **Cyan text**: Highlighted information like VIN numbers

### Sample Output
```
🚗 Tesla Model Y Juniper Bot Started
Target: Model Y RWD (Juniper) - Max Price: 2,000,000 TRY
Checking every 100ms (very fast monitoring!)...
⚠️  High frequency monitoring - be careful of rate limits!

[14:30:15] No vehicles found. Check #1000 - Continuing to monitor...
[14:30:25] No vehicles found. Check #2000 - Continuing to monitor...

🚗 VEHICLE FOUND!
==================================================
VIN: 5YJ3E1EA8PF123456
Price: 1,899,000 TRY
Model: my
Year: 2024
Trim: MYRWD
Options: $MTY01, $PBSB, $INYPB
Status: Available
==================================================
```

## 📁 Project Structure

```
TeslaBot/
├── tesla_bot.py          # Main bot script
├── config.py             # Configuration settings
├── requirements.txt      # Python dependencies
├── .env.example         # Environment variables template
├── .env                 # Your personal configuration (create this)
├── tesla_bot.log        # Log file (created automatically)
├── README.md            # This file
└── InventoryPage.html   # Tesla inventory page analysis
```

## 🔍 How It Works

1. **API Analysis**: The bot uses Tesla's internal inventory API endpoint
2. **Continuous Monitoring**: Sends requests every 30 seconds to check for new inventory
3. **Filtering**: Only shows vehicles matching Model Y Juniper criteria
4. **Real-time Updates**: Displays found vehicles immediately with full details

## ⚙️ Advanced Configuration

You can modify `config.py` to adjust:

- `CHECK_INTERVAL`: Time between inventory checks (default: 0.1 seconds = 100ms)
- `MAX_PRICE`: Maximum acceptable price (default: 2,000,000 TL)
- `TARGET_OPTION_CODES`: Specific option codes to look for
- `REQUEST_TIMEOUT`: API request timeout (default: 10 seconds)

## 🐛 Troubleshooting

### Common Issues

1. **"No module named 'requests'"**
   - Run: `pip install -r requirements.txt`

2. **"API request failed: 403"**
   - Tesla may be blocking requests. Try changing user agent or adding delays

3. **"No vehicles found" continuously**
   - This is normal when no inventory is available
   - The bot will alert you immediately when vehicles appear

4. **Connection errors**
   - Check your internet connection
   - Tesla's servers might be temporarily unavailable

### Logs

Check `tesla_bot.log` for detailed information about:
- API requests and responses
- Error messages
- Vehicle detection events

## 🚨 Next Steps (Order Automation)

This current version only monitors inventory. The next phase will include:
- Automated form filling
- Order placement with manual CAPTCHA handling
- Payment processing automation
- Order confirmation

## 📞 Support

If you encounter issues:
1. Check the log file (`tesla_bot.log`)
2. Verify your `.env` configuration
3. Ensure all dependencies are installed
4. Check your internet connection

## ⚖️ Legal Notice

This software is for educational purposes. Users are responsible for:
- Complying with Tesla's Terms of Service
- Following Turkish laws and regulations
- Using the software ethically and responsibly

The authors are not responsible for any consequences of using this software.

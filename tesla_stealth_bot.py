#!/usr/bin/env python3
"""
Tesla Model Y Juniper Stealth Bot - Better approach to avoid 403 errors
"""

import time
import json
import logging
import requests
from datetime import datetime
from typing import Dict, List, Optional, Any
from colorama import init, Fore, Style
from fake_useragent import UserAgent
import re

import config

# Initialize colorama
init(autoreset=True)

# Setup logging
logging.basicConfig(
    level=getattr(logging, config.LOG_LEVEL),
    format=config.LOG_FORMAT,
    handlers=[
        logging.FileHandler('tesla_stealth_bot.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class TeslaStealthMonitor:
    """Stealth Tesla inventory monitor that mimics real browser behavior"""
    
    def __init__(self):
        self.session = requests.Session()
        self.ua = UserAgent()
        self.csrf_token = None
        self.setup_session()
        self.initialize_session()
        
    def setup_session(self):
        """Setup session with realistic browser headers"""
        headers = config.DEFAULT_HEADERS.copy()
        headers["User-Agent"] = self.ua.chrome
        self.session.headers.update(headers)
        
    def initialize_session(self):
        """Initialize session by visiting Tesla page first like a real browser"""
        try:
            print(f"{Fore.BLUE}🔧 Initializing session...{Style.RESET_ALL}", flush=True)
            
            # First, visit the main inventory page
            inventory_url = f"{config.TESLA_BASE_URL}/tr_tr/inventory/new/my"
            response = self.session.get(inventory_url, timeout=config.REQUEST_TIMEOUT)
            
            if response.status_code == 200:
                # Extract CSRF token from the page
                csrf_match = re.search(r'"csrf_token":"([^"]+)"', response.text)
                if csrf_match:
                    self.csrf_token = csrf_match.group(1)
                    print(f"{Fore.GREEN}✅ Session initialized with CSRF token{Style.RESET_ALL}", flush=True)
                else:
                    print(f"{Fore.YELLOW}⚠️  Session initialized but no CSRF token found{Style.RESET_ALL}", flush=True)
                
                # Add any cookies from the response
                time.sleep(1)  # Small delay to appear more human
                return True
            else:
                print(f"{Fore.RED}❌ Failed to initialize session: {response.status_code}{Style.RESET_ALL}", flush=True)
                return False
                
        except Exception as e:
            print(f"{Fore.RED}❌ Session initialization error: {e}{Style.RESET_ALL}", flush=True)
            return False
    
    def build_inventory_query(self) -> Dict[str, Any]:
        """Build inventory query with proper format"""
        query = {
            "query": {
                "model": config.TARGET_MODEL,
                "condition": config.TARGET_CONDITION,
                "options": {
                    "TRIM": [config.TARGET_TRIM]
                },
                "market": config.MARKET,
                "language": "tr",
                "arrangeby": "Price",
                "order": "asc",
                "lng": "tr",
                "skipVin": []
            }
        }
        
        return query
    
    def check_inventory(self) -> List[Dict[str, Any]]:
        """Check inventory with stealth approach"""
        try:
            url = f"{config.TESLA_BASE_URL}{config.INVENTORY_API_ENDPOINT}"
            query = self.build_inventory_query()
            
            # Add CSRF token if we have it
            headers = {}
            if self.csrf_token:
                headers["X-Tesla-CSRF-Token"] = self.csrf_token
            
            response = self.session.post(
                url,
                json=query,
                headers=headers,
                timeout=config.REQUEST_TIMEOUT
            )
            
            if response.status_code == 200:
                data = response.json()
                results = data.get('results', [])
                
                # Filter for target vehicles
                available_vehicles = []
                for vehicle in results:
                    if self.is_target_vehicle(vehicle):
                        available_vehicles.append(vehicle)
                
                return available_vehicles
                
            elif response.status_code == 403:
                print(f"{Fore.YELLOW}⚠️  403 Forbidden - Re-initializing session...{Style.RESET_ALL}", flush=True)
                self.initialize_session()
                return []
                
            elif response.status_code == 429:
                print(f"{Fore.YELLOW}⚠️  Rate limited - Waiting 30 seconds...{Style.RESET_ALL}", flush=True)
                time.sleep(30)
                return []
                
            else:
                print(f"{Fore.RED}❌ API error: {response.status_code}{Style.RESET_ALL}", flush=True)
                return []
                
        except Exception as e:
            print(f"{Fore.RED}❌ Check error: {e}{Style.RESET_ALL}", flush=True)
            return []
    
    def is_target_vehicle(self, vehicle: Dict[str, Any]) -> bool:
        """Check if vehicle matches target criteria"""
        try:
            # Check model
            if vehicle.get('Model') != config.TARGET_MODEL:
                return False
            
            # Check condition
            if vehicle.get('TitleStatus') != config.TARGET_CONDITION:
                return False
            
            # Check price
            price = vehicle.get('Price', 0)
            if price > config.MAX_PRICE:
                return False
            
            # Check for RWD options
            options = vehicle.get('OptionCodeList', [])
            if not any(code in options for code in config.TARGET_OPTION_CODES):
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking vehicle: {e}")
            return False
    
    def display_vehicle_info(self, vehicle: Dict[str, Any]):
        """Display found vehicle information"""
        print(f"\n{Fore.GREEN}🚗 TESLA MODEL Y JUNIPER FOUND!{Style.RESET_ALL}", flush=True)
        print(f"{Fore.YELLOW}{'='*60}{Style.RESET_ALL}", flush=True)
        
        vin = vehicle.get('VIN', 'N/A')
        price = vehicle.get('Price', 0)
        currency = vehicle.get('CurrencyCode', config.CURRENCY)
        
        print(f"VIN: {Fore.CYAN}{vin}{Style.RESET_ALL}", flush=True)
        print(f"Price: {Fore.GREEN}{price:,} {currency}{Style.RESET_ALL}", flush=True)
        print(f"Model: {vehicle.get('Model', 'N/A')}", flush=True)
        print(f"Year: {vehicle.get('Year', 'N/A')}", flush=True)
        
        # Display options
        options = vehicle.get('OptionCodeList', [])
        if options:
            print(f"Options: {', '.join(options)}", flush=True)
        
        print(f"{Fore.YELLOW}{'='*60}{Style.RESET_ALL}\n", flush=True)

def main():
    """Main monitoring function"""
    print(f"{Fore.CYAN}🚗 Tesla Model Y Juniper Stealth Bot{Style.RESET_ALL}", flush=True)
    print(f"{Fore.YELLOW}Target: Model Y RWD (Juniper) - Max Price: {config.MAX_PRICE:,} {config.CURRENCY}{Style.RESET_ALL}", flush=True)
    print(f"{Fore.BLUE}Checking every {config.CHECK_INTERVAL} seconds (stealth mode){Style.RESET_ALL}", flush=True)
    print("=" * 60, flush=True)
    
    monitor = TeslaStealthMonitor()
    check_count = 0
    
    try:
        while True:
            check_count += 1
            current_time = datetime.now().strftime("%H:%M:%S")
            
            print(f"{Fore.BLUE}[{current_time}] Check #{check_count} - Searching...{Style.RESET_ALL}", end=" ", flush=True)
            
            vehicles = monitor.check_inventory()
            
            if vehicles:
                print(f"{Fore.GREEN}FOUND {len(vehicles)} VEHICLE(S)!{Style.RESET_ALL}", flush=True)
                for vehicle in vehicles:
                    monitor.display_vehicle_info(vehicle)
            else:
                print(f"{Fore.BLUE}No vehicles{Style.RESET_ALL}", flush=True)
            
            time.sleep(config.CHECK_INTERVAL)
            
    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}🛑 Bot stopped by user{Style.RESET_ALL}", flush=True)
        print(f"{Fore.CYAN}Total checks: {check_count}{Style.RESET_ALL}", flush=True)

if __name__ == "__main__":
    main()

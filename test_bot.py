#!/usr/bin/env python3
"""
Simple test script for Tesla Bot
"""

import sys
import time
from colorama import init, Fore, Style

# Initialize colorama
init(autoreset=True)

def test_imports():
    """Test all imports"""
    print(f"{Fore.BLUE}Testing imports...{Style.RESET_ALL}")
    
    try:
        import config
        print(f"{Fore.GREEN}✅ Config imported{Style.RESET_ALL}")
        
        from tesla_bot import TeslaInventoryMonitor
        print(f"{Fore.GREEN}✅ TeslaInventoryMonitor imported{Style.RESET_ALL}")
        
        return True
    except Exception as e:
        print(f"{Fore.RED}❌ Import error: {e}{Style.RESET_ALL}")
        return False

def test_monitor():
    """Test the monitor creation and basic functionality"""
    print(f"{Fore.BLUE}Testing monitor creation...{Style.RESET_ALL}")
    
    try:
        from tesla_bot import TeslaInventoryMonitor
        monitor = TeslaInventoryMonitor()
        print(f"{Fore.GREEN}✅ Monitor created successfully{Style.RESET_ALL}")
        
        # Test query building
        query = monitor.build_inventory_query()
        print(f"{Fore.GREEN}✅ Query built: {query['query']['model']}{Style.RESET_ALL}")
        
        return monitor
    except Exception as e:
        print(f"{Fore.RED}❌ Monitor creation error: {e}{Style.RESET_ALL}")
        return None

def test_single_check(monitor):
    """Test a single inventory check"""
    print(f"{Fore.BLUE}Testing single inventory check...{Style.RESET_ALL}")
    
    try:
        vehicles = monitor.check_inventory()
        print(f"{Fore.GREEN}✅ Check completed. Found {len(vehicles)} vehicles{Style.RESET_ALL}")
        
        if vehicles:
            for vehicle in vehicles:
                monitor.display_vehicle_info(vehicle)
        else:
            print(f"{Fore.YELLOW}No vehicles found (this is normal){Style.RESET_ALL}")
            
        return True
    except Exception as e:
        print(f"{Fore.RED}❌ Check error: {e}{Style.RESET_ALL}")
        return False

def main():
    """Main test function"""
    print(f"{Fore.CYAN}🚗 Tesla Bot Test Script{Style.RESET_ALL}")
    print("=" * 40)
    
    # Test imports
    if not test_imports():
        return
    
    # Test monitor creation
    monitor = test_monitor()
    if not monitor:
        return
    
    # Test single check
    if not test_single_check(monitor):
        return
    
    print(f"\n{Fore.GREEN}🎉 All tests passed!{Style.RESET_ALL}")
    print(f"{Fore.BLUE}The bot should work correctly.{Style.RESET_ALL}")
    
    # Ask if user wants to start continuous monitoring
    print(f"\n{Fore.YELLOW}Would you like to start continuous monitoring? (y/n){Style.RESET_ALL}")
    
    # For automated testing, let's just show the option
    print(f"{Fore.CYAN}To start continuous monitoring, run: python tesla_bot.py{Style.RESET_ALL}")

if __name__ == "__main__":
    main()

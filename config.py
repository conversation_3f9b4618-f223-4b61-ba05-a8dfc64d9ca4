"""
Tesla Model Y Juniper Bot Configuration
"""
import os
from dotenv import load_dotenv

load_dotenv()

# Tesla API Configuration
TESLA_BASE_URL = "https://www.tesla.com"
INVENTORY_API_ENDPOINT = "/coinorder/api/v4/inventory-results"
MARKET = "TR"
LOCALE = "tr_TR"
CURRENCY = "TRY"

# Target Vehicle Configuration
TARGET_MODEL = "my"  # Model Y
TARGET_CONDITION = "new"
TARGET_TRIM = "MYRWD"  # Model Y Rear-Wheel Drive (Standard Range)
TARGET_OPTION_CODES = ["$MTY01", "$MTY08"]  # Model Y RWD option codes
MAX_PRICE = 2000000  # 2M TL max (you mentioned 1.9M TL target)

# Monitoring Configuration
CHECK_INTERVAL = 30  # seconds between checks
MAX_RETRIES = 3
REQUEST_TIMEOUT = 10

# User Information (to be filled by user)
USER_INFO = {
    "first_name": os.getenv("FIRST_NAME", ""),
    "last_name": os.getenv("LAST_NAME", ""),
    "email": os.getenv("EMAIL", ""),
    "phone": os.getenv("PHONE", ""),
    "address": os.getenv("ADDRESS", ""),
    "city": os.getenv("CITY", ""),
    "postal_code": os.getenv("POSTAL_CODE", ""),
    "id_number": os.getenv("ID_NUMBER", ""),  # Turkish ID number
}

# Payment Information (to be filled by user)
PAYMENT_INFO = {
    "card_number": os.getenv("CARD_NUMBER", ""),
    "card_holder": os.getenv("CARD_HOLDER", ""),
    "expiry_month": os.getenv("EXPIRY_MONTH", ""),
    "expiry_year": os.getenv("EXPIRY_YEAR", ""),
    "cvv": os.getenv("CVV", ""),
}

# Browser Configuration
HEADLESS_MODE = False  # Set to True for headless operation
BROWSER_TIMEOUT = 30
IMPLICIT_WAIT = 10

# Headers for requests
DEFAULT_HEADERS = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Accept": "application/json, text/plain, */*",
    "Accept-Language": "tr-TR,tr;q=0.9,en;q=0.8",
    "Accept-Encoding": "gzip, deflate, br",
    "Connection": "keep-alive",
    "Referer": f"{TESLA_BASE_URL}/tr_tr/inventory/new/my",
    "Origin": TESLA_BASE_URL,
}

# Logging Configuration
LOG_LEVEL = "INFO"
LOG_FORMAT = "%(asctime)s - %(levelname)s - %(message)s"

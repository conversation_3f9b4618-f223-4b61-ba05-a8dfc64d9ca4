#!/usr/bin/env python3
"""
Quick Tesla Bot Test - Shows immediate output
"""

import time
import sys
from colorama import init, Fore, Style
from tesla_bot import TeslaInventoryMonitor

# Initialize colorama
init(autoreset=True)

def main():
    print("🚗 Tesla Bot Quick Test", flush=True)
    print("=" * 30, flush=True)
    
    monitor = TeslaInventoryMonitor()
    print("✅ Monitor created", flush=True)
    
    print("🔍 Starting 10 quick checks...", flush=True)
    
    for i in range(10):
        print(f"Check {i+1}/10...", end=" ", flush=True)
        
        vehicles = monitor.check_inventory()
        
        if vehicles:
            print(f"🎉 FOUND {len(vehicles)} VEHICLES!", flush=True)
            for vehicle in vehicles:
                monitor.display_vehicle_info(vehicle)
        else:
            print("No vehicles", flush=True)
        
        time.sleep(1)  # 1 second between checks for testing
    
    print("\n✅ Test completed!", flush=True)
    print("The bot is working correctly.", flush=True)

if __name__ == "__main__":
    main()

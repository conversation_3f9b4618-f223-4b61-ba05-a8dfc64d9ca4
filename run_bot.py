#!/usr/bin/env python3
"""
Quick launcher for Tesla Bot with setup validation
"""

import os
import sys
from pathlib import Path

def check_requirements():
    """Check if all requirements are met"""
    print("🔍 Checking requirements...")
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher required")
        return False
    print("✅ Python version OK")
    
    # Check if .env file exists
    if not Path('.env').exists():
        print("❌ .env file not found")
        print("📝 Please copy .env.example to .env and fill in your information")
        return False
    print("✅ .env file found")
    
    # Check required packages
    required_packages = ['requests', 'colorama', 'python-dotenv', 'fake-useragent']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ Missing packages: {', '.join(missing_packages)}")
        print("📦 Run: pip install -r requirements.txt")
        return False
    print("✅ All packages installed")
    
    return True

def main():
    """Main launcher function"""
    print("🚗 Tesla Model Y Juniper Bot Launcher")
    print("=" * 40)
    
    if not check_requirements():
        print("\n❌ Setup incomplete. Please fix the issues above.")
        return
    
    print("\n✅ All checks passed!")
    print("🚀 Starting Tesla Bot...")
    print("⚠️  Press Ctrl+C to stop the bot")
    print("=" * 40)
    
    # Import and run the bot
    try:
        from tesla_bot import main as run_bot
        run_bot()
    except KeyboardInterrupt:
        print("\n👋 Bot stopped by user")
    except Exception as e:
        print(f"\n❌ Error running bot: {e}")
        print("📋 Check tesla_bot.log for details")

if __name__ == "__main__":
    main()

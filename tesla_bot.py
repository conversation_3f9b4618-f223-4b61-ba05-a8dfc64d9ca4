#!/usr/bin/env python3
"""
Tesla Model Y Juniper Order Bot
Monitors Tesla Turkey inventory and attempts to place orders automatically
"""

import time
import json
import logging
import requests
from datetime import datetime
from typing import Dict, List, Optional, Any
from colorama import init, Fore, Style
from fake_useragent import UserAgent

import config

# Initialize colorama for colored output
init(autoreset=True)

# Setup logging
logging.basicConfig(
    level=getattr(logging, config.LOG_LEVEL),
    format=config.LOG_FORMAT,
    handlers=[
        logging.FileHandler('tesla_bot.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class TeslaInventoryMonitor:
    """Monitors Tesla inventory for Model Y Juniper availability"""
    
    def __init__(self):
        self.session = requests.Session()
        self.ua = UserAgent()
        self.setup_session()
        self.last_check_time = None
        self.found_vehicles = []
        
    def setup_session(self):
        """Setup requests session with proper headers and cookies"""
        headers = config.DEFAULT_HEADERS.copy()
        headers["User-Agent"] = self.ua.random
        self.session.headers.update(headers)

        # Add session cookies to appear more like a real browser
        self.session.cookies.update({
            'tesla_locale': 'tr_TR',
            'tesla_market': 'TR'
        })
        
    def build_inventory_query(self) -> Dict[str, Any]:
        """Build the inventory search query matching Tesla's format"""
        return {
            "query": {
                "model": config.TARGET_MODEL,
                "condition": config.TARGET_CONDITION,
                "options": {
                    "TRIM": [config.TARGET_TRIM]
                },
                "market": config.MARKET,
                "language": config.LOCALE.split('_')[0],
                "arrangeby": "Price",
                "order": "asc",
                "lng": config.LOCALE.split('_')[0],
                "skipVin": [],
                "region": config.MARKET,
                "lng": "tr"
            }
        }
    
    def check_inventory(self) -> List[Dict[str, Any]]:
        """Check Tesla inventory for available vehicles"""
        try:
            url = f"{config.TESLA_BASE_URL}{config.INVENTORY_API_ENDPOINT}"
            query = self.build_inventory_query()

            # Only log every 100 checks to avoid spam (every 10 seconds at 100ms interval)
            check_count = getattr(self, 'check_count', 0)
            self.check_count = check_count + 1

            if self.check_count % 100 == 0:
                logger.info(f"{Fore.BLUE}Checking inventory... (Check #{self.check_count}){Style.RESET_ALL}")

            response = self.session.post(
                url,
                json=query,
                timeout=config.REQUEST_TIMEOUT
            )
            
            if response.status_code == 200:
                data = response.json()
                results = data.get('results', [])

                # Filter for our target vehicles
                available_vehicles = []
                for vehicle in results:
                    if self.is_target_vehicle(vehicle):
                        available_vehicles.append(vehicle)

                self.last_check_time = datetime.now()
                return available_vehicles
            elif response.status_code == 429:
                # Rate limited - wait a bit longer
                logger.warning(f"{Fore.YELLOW}Rate limited. Waiting 10 seconds...{Style.RESET_ALL}")
                time.sleep(10)
                return []
            elif response.status_code == 403:
                # Forbidden - Tesla is blocking us, need to be more careful
                if self.check_count % 10 == 0:  # Only log every 10th 403 error
                    logger.warning(f"{Fore.YELLOW}403 Forbidden - Tesla blocking requests. Slowing down...{Style.RESET_ALL}")
                time.sleep(5)  # Wait 5 seconds on 403
                return []
            else:
                # Only log errors occasionally to avoid spam
                if self.check_count % 50 == 0:
                    logger.error(f"API request failed: {response.status_code}")
                return []

        except Exception as e:
            # Only log errors occasionally to avoid spam
            if getattr(self, 'check_count', 0) % 50 == 0:
                logger.error(f"Error checking inventory: {e}")
            return []
    
    def is_target_vehicle(self, vehicle: Dict[str, Any]) -> bool:
        """Check if vehicle matches our target criteria"""
        try:
            # Check model
            if vehicle.get('Model') != config.TARGET_MODEL:
                return False
            
            # Check condition
            if vehicle.get('TitleStatus') != config.TARGET_CONDITION:
                return False
            
            # Check trim/options
            options = vehicle.get('OptionCodeList', [])
            if not any(code in options for code in config.TARGET_OPTION_CODES):
                return False
            
            # Check price
            price = vehicle.get('Price', 0)
            if price > config.MAX_PRICE:
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking vehicle criteria: {e}")
            return False
    
    def display_vehicle_info(self, vehicle: Dict[str, Any]):
        """Display vehicle information in a formatted way"""
        print(f"\n{Fore.GREEN}🚗 VEHICLE FOUND!{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}{'='*50}{Style.RESET_ALL}")
        
        vin = vehicle.get('VIN', 'N/A')
        price = vehicle.get('Price', 0)
        currency = vehicle.get('CurrencyCode', config.CURRENCY)
        
        print(f"VIN: {Fore.CYAN}{vin}{Style.RESET_ALL}")
        print(f"Price: {Fore.GREEN}{price:,} {currency}{Style.RESET_ALL}")
        print(f"Model: {vehicle.get('Model', 'N/A')}")
        print(f"Year: {vehicle.get('Year', 'N/A')}")
        print(f"Trim: {vehicle.get('TRIM', 'N/A')}")
        
        # Display options
        options = vehicle.get('OptionCodeList', [])
        if options:
            print(f"Options: {', '.join(options)}")
        
        # Display availability
        availability = vehicle.get('AvailabilityStatus', 'Unknown')
        print(f"Status: {Fore.GREEN if 'available' in availability.lower() else Fore.YELLOW}{availability}{Style.RESET_ALL}")
        
        print(f"{Fore.YELLOW}{'='*50}{Style.RESET_ALL}\n")

def main():
    """Main function to run the Tesla inventory monitor"""
    print(f"{Fore.CYAN}🚗 Tesla Model Y Juniper Bot Started{Style.RESET_ALL}", flush=True)
    print(f"{Fore.YELLOW}Target: Model Y RWD (Juniper) - Max Price: {config.MAX_PRICE:,} {config.CURRENCY}{Style.RESET_ALL}", flush=True)
    print(f"{Fore.BLUE}Checking every {config.CHECK_INTERVAL*1000:.0f}ms (very fast monitoring!)...{Style.RESET_ALL}", flush=True)
    print(f"{Fore.RED}⚠️  High frequency monitoring - be careful of rate limits!{Style.RESET_ALL}\n", flush=True)
    
    monitor = TeslaInventoryMonitor()
    
    try:
        while True:
            vehicles = monitor.check_inventory()
            
            if vehicles:
                for vehicle in vehicles:
                    monitor.display_vehicle_info(vehicle)
                    
                print(f"{Fore.RED}⚠️  VEHICLE(S) FOUND! Check above for details.{Style.RESET_ALL}")
                print(f"{Fore.YELLOW}The bot will continue monitoring for more vehicles...{Style.RESET_ALL}\n")
                
                # Store found vehicles
                monitor.found_vehicles.extend(vehicles)
            else:
                # Only print status every 10 seconds (100 checks at 100ms)
                if monitor.check_count % 100 == 0:
                    current_time = datetime.now().strftime("%H:%M:%S")
                    print(f"{Fore.BLUE}[{current_time}] No vehicles found. Check #{monitor.check_count} - Continuing to monitor...{Style.RESET_ALL}", flush=True)
            
            time.sleep(config.CHECK_INTERVAL)
            
    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}Bot stopped by user.{Style.RESET_ALL}")
        if monitor.found_vehicles:
            print(f"{Fore.GREEN}Total vehicles found during session: {len(monitor.found_vehicles)}{Style.RESET_ALL}")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        print(f"{Fore.RED}Bot encountered an error: {e}{Style.RESET_ALL}")

if __name__ == "__main__":
    main()

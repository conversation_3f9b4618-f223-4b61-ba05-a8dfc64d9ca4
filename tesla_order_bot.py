#!/usr/bin/env python3
"""
Tesla Model Y Juniper Order Bot - Complete automation with form filling and CAPTCHA handling
"""

import time
import os
from datetime import datetime
from colorama import init, Fore, Style
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.keys import Keys

from tesla_final_bot import TeslaFinalBot
import config

# Initialize colorama
init(autoreset=True)

class TeslaOrderBot:
    """Complete Tesla order automation with monitoring, form filling, and CAPTCHA handling"""
    
    def __init__(self):
        self.monitor = TeslaFinalBot()
        self.driver = None
        self.wait = None
        
    def setup_browser(self):
        """Setup Chrome browser for order automation"""
        print(f"{Fore.BLUE}🔧 Setting up browser for order automation...{Style.RESET_ALL}", flush=True)
        
        chrome_options = Options()
        chrome_options.add_argument("--start-maximized")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # Install and setup ChromeDriver
        service = Service(ChromeDriverManager().install())
        self.driver = webdriver.Chrome(service=service, options=chrome_options)
        self.wait = WebDriverWait(self.driver, 30)
        
        # Make browser appear more human-like
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        print(f"{Fore.GREEN}✅ Browser ready for automation{Style.RESET_ALL}", flush=True)
    
    def navigate_to_vehicle(self, vehicle):
        """Navigate to specific vehicle order page"""
        try:
            vin = vehicle.get('VIN')
            print(f"{Fore.BLUE}🌐 Navigating to vehicle order page...{Style.RESET_ALL}", flush=True)
            
            # Navigate to Tesla inventory
            self.driver.get("https://www.tesla.com/tr_tr/inventory/new/my")
            time.sleep(3)
            
            # Look for the specific vehicle or similar
            print(f"{Fore.BLUE}🔍 Looking for vehicle with VIN: {vin}...{Style.RESET_ALL}", flush=True)
            
            # Click on first available Model Y (since we filtered for our target)
            try:
                order_button = self.wait.until(
                    EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Sipariş') or contains(text(), 'Order')]"))
                )
                order_button.click()
                print(f"{Fore.GREEN}✅ Clicked order button{Style.RESET_ALL}", flush=True)
                time.sleep(3)
                return True
            except:
                print(f"{Fore.YELLOW}⚠️  Order button not found, trying alternative method...{Style.RESET_ALL}", flush=True)
                return False
                
        except Exception as e:
            print(f"{Fore.RED}❌ Navigation error: {e}{Style.RESET_ALL}", flush=True)
            return False
    
    def fill_personal_info(self):
        """Fill personal information forms"""
        print(f"{Fore.BLUE}👤 Filling personal information...{Style.RESET_ALL}", flush=True)
        
        try:
            # Fill first name
            first_name_field = self.wait.until(EC.presence_of_element_located((By.NAME, "firstName")))
            first_name_field.clear()
            first_name_field.send_keys(config.USER_INFO['first_name'])
            print(f"   ✅ First name: {config.USER_INFO['first_name']}", flush=True)
            
            # Fill last name
            last_name_field = self.driver.find_element(By.NAME, "lastName")
            last_name_field.clear()
            last_name_field.send_keys(config.USER_INFO['last_name'])
            print(f"   ✅ Last name: {config.USER_INFO['last_name']}", flush=True)
            
            # Fill email
            email_field = self.driver.find_element(By.NAME, "email")
            email_field.clear()
            email_field.send_keys(config.USER_INFO['email'])
            print(f"   ✅ Email: {config.USER_INFO['email']}", flush=True)
            
            # Fill phone
            phone_field = self.driver.find_element(By.NAME, "phone")
            phone_field.clear()
            phone_field.send_keys(config.USER_INFO['phone'])
            print(f"   ✅ Phone: {config.USER_INFO['phone']}", flush=True)
            
            time.sleep(1)
            return True
            
        except Exception as e:
            print(f"{Fore.RED}❌ Personal info error: {e}{Style.RESET_ALL}", flush=True)
            return False
    
    def fill_address_info(self):
        """Fill address information"""
        print(f"{Fore.BLUE}🏠 Filling address information...{Style.RESET_ALL}", flush=True)
        
        try:
            # Fill address
            address_field = self.driver.find_element(By.NAME, "address")
            address_field.clear()
            address_field.send_keys(config.USER_INFO['address'])
            print(f"   ✅ Address: {config.USER_INFO['address']}", flush=True)
            
            # Fill city
            city_field = self.driver.find_element(By.NAME, "city")
            city_field.clear()
            city_field.send_keys(config.USER_INFO['city'])
            print(f"   ✅ City: {config.USER_INFO['city']}", flush=True)
            
            # Fill postal code
            postal_field = self.driver.find_element(By.NAME, "postalCode")
            postal_field.clear()
            postal_field.send_keys(config.USER_INFO['postal_code'])
            print(f"   ✅ Postal code: {config.USER_INFO['postal_code']}", flush=True)
            
            time.sleep(1)
            return True
            
        except Exception as e:
            print(f"{Fore.RED}❌ Address info error: {e}{Style.RESET_ALL}", flush=True)
            return False
    
    def fill_payment_info(self):
        """Fill payment information"""
        print(f"{Fore.BLUE}💳 Filling payment information...{Style.RESET_ALL}", flush=True)
        
        try:
            # Fill card number
            card_field = self.driver.find_element(By.NAME, "cardNumber")
            card_field.clear()
            card_field.send_keys(config.PAYMENT_INFO['card_number'])
            card_masked = config.PAYMENT_INFO['card_number'][:4] + "****" + config.PAYMENT_INFO['card_number'][-4:]
            print(f"   ✅ Card number: {card_masked}", flush=True)
            
            # Fill cardholder name
            holder_field = self.driver.find_element(By.NAME, "cardHolder")
            holder_field.clear()
            holder_field.send_keys(config.PAYMENT_INFO['card_holder'])
            print(f"   ✅ Cardholder: {config.PAYMENT_INFO['card_holder']}", flush=True)
            
            # Fill expiry month
            month_field = self.driver.find_element(By.NAME, "expiryMonth")
            month_field.clear()
            month_field.send_keys(config.PAYMENT_INFO['expiry_month'])
            
            # Fill expiry year
            year_field = self.driver.find_element(By.NAME, "expiryYear")
            year_field.clear()
            year_field.send_keys(config.PAYMENT_INFO['expiry_year'])
            print(f"   ✅ Expiry: {config.PAYMENT_INFO['expiry_month']}/{config.PAYMENT_INFO['expiry_year']}", flush=True)
            
            # Fill CVV
            cvv_field = self.driver.find_element(By.NAME, "cvv")
            cvv_field.clear()
            cvv_field.send_keys(config.PAYMENT_INFO['cvv'])
            print(f"   ✅ CVV: ***", flush=True)
            
            time.sleep(1)
            return True
            
        except Exception as e:
            print(f"{Fore.RED}❌ Payment info error: {e}{Style.RESET_ALL}", flush=True)
            return False
    
    def handle_captcha(self):
        """Handle CAPTCHA - wait for manual solving"""
        print(f"\n{Fore.YELLOW}🔐 CAPTCHA DETECTED!{Style.RESET_ALL}", flush=True)
        print(f"{Fore.CYAN}🤖 Bot paused - Manual action required{Style.RESET_ALL}", flush=True)
        print(f"{Fore.YELLOW}👤 Please solve the CAPTCHA in the browser window{Style.RESET_ALL}", flush=True)
        
        # Focus browser window
        self.driver.switch_to.window(self.driver.current_window_handle)
        
        # Wait for CAPTCHA to be solved
        print(f"{Fore.BLUE}⏳ Waiting for CAPTCHA to be solved...{Style.RESET_ALL}", flush=True)
        
        captcha_solved = False
        timeout = 300  # 5 minutes timeout
        start_time = time.time()
        
        while not captcha_solved and (time.time() - start_time) < timeout:
            try:
                # Check if CAPTCHA is still present
                captcha_elements = self.driver.find_elements(By.XPATH, "//*[contains(@class, 'captcha') or contains(@id, 'captcha')]")
                if not captcha_elements:
                    captcha_solved = True
                    break
                
                time.sleep(2)
                print(f"{Fore.BLUE}   Still waiting for CAPTCHA...{Style.RESET_ALL}", flush=True)
                
            except:
                # If we can't find CAPTCHA elements, assume it's solved
                captcha_solved = True
                break
        
        if captcha_solved:
            print(f"{Fore.GREEN}✅ CAPTCHA solved! Continuing...{Style.RESET_ALL}", flush=True)
            return True
        else:
            print(f"{Fore.RED}❌ CAPTCHA timeout - please try again{Style.RESET_ALL}", flush=True)
            return False
    
    def submit_order(self):
        """Submit the final order"""
        print(f"\n{Fore.CYAN}🚀 Submitting order...{Style.RESET_ALL}", flush=True)
        
        try:
            # Look for submit/place order button
            submit_button = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Place Order') or contains(text(), 'Sipariş Ver')]"))
            )
            
            print(f"{Fore.YELLOW}⚠️  FINAL CONFIRMATION: About to place order!{Style.RESET_ALL}", flush=True)
            print(f"{Fore.RED}🚨 This will charge your payment method{Style.RESET_ALL}", flush=True)
            
            # Final countdown
            for i in range(3, 0, -1):
                print(f"{Fore.RED}   Submitting in {i}...{Style.RESET_ALL}", flush=True)
                time.sleep(1)
            
            submit_button.click()
            print(f"{Fore.GREEN}✅ ORDER SUBMITTED!{Style.RESET_ALL}", flush=True)
            
            time.sleep(5)  # Wait for confirmation
            return True
            
        except Exception as e:
            print(f"{Fore.RED}❌ Order submission error: {e}{Style.RESET_ALL}", flush=True)
            return False
    
    def process_order(self, vehicle):
        """Complete order process for found vehicle"""
        print(f"\n{Fore.GREEN}🎯 Starting order automation for vehicle...{Style.RESET_ALL}", flush=True)
        
        try:
            # Setup browser
            self.setup_browser()
            
            # Navigate to vehicle
            if not self.navigate_to_vehicle(vehicle):
                return False
            
            # Fill forms
            if not self.fill_personal_info():
                return False
            
            if not self.fill_address_info():
                return False
            
            if not self.fill_payment_info():
                return False
            
            # Handle CAPTCHA
            if not self.handle_captcha():
                return False
            
            # Submit order
            if not self.submit_order():
                return False
            
            print(f"\n{Fore.GREEN}🎉 ORDER COMPLETED SUCCESSFULLY!{Style.RESET_ALL}", flush=True)
            return True
            
        except Exception as e:
            print(f"{Fore.RED}❌ Order process error: {e}{Style.RESET_ALL}", flush=True)
            return False
        
        finally:
            if self.driver:
                input(f"\n{Fore.BLUE}Press Enter to close browser...{Style.RESET_ALL}")
                self.driver.quit()
    
    def run_with_monitoring(self):
        """Run bot with inventory monitoring and automatic ordering"""
        print(f"{Fore.CYAN}🚗 Tesla Model Y Juniper Order Bot - LIVE MODE{Style.RESET_ALL}", flush=True)
        print(f"{Fore.YELLOW}🎯 Monitoring + Automatic Ordering Enabled{Style.RESET_ALL}", flush=True)
        print("=" * 60, flush=True)
        
        check_count = 0
        
        try:
            while True:
                check_count += 1
                current_time = datetime.now().strftime("%H:%M:%S")
                
                print(f"{Fore.BLUE}[{current_time}] Check #{check_count:,} - Monitoring...{Style.RESET_ALL}", end=" ", flush=True)
                
                vehicles = self.monitor.check_inventory()
                
                if vehicles:
                    print(f"{Fore.GREEN}FOUND {len(vehicles)} VEHICLE(S)!{Style.RESET_ALL}", flush=True)
                    
                    for vehicle in vehicles:
                        self.monitor.display_vehicle_info(vehicle)
                        
                        # Ask user if they want to proceed with order
                        response = input(f"{Fore.YELLOW}🤖 Proceed with automatic order? (y/n): {Style.RESET_ALL}")
                        
                        if response.lower() == 'y':
                            success = self.process_order(vehicle)
                            if success:
                                print(f"{Fore.GREEN}🎉 Order completed! Stopping bot.{Style.RESET_ALL}", flush=True)
                                return
                        else:
                            print(f"{Fore.BLUE}⏭️  Skipping this vehicle, continuing monitoring...{Style.RESET_ALL}", flush=True)
                else:
                    print(f"{Fore.BLUE}No vehicles{Style.RESET_ALL}", flush=True)
                
                time.sleep(config.CHECK_INTERVAL)
                
        except KeyboardInterrupt:
            print(f"\n{Fore.YELLOW}🛑 Bot stopped by user{Style.RESET_ALL}", flush=True)

def main():
    """Main function"""
    bot = TeslaOrderBot()
    bot.run_with_monitoring()

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Tesla Model Y Juniper Final Bot - Working version based on exact Tesla API format
"""

import time
import json
import logging
import requests
from datetime import datetime
from typing import Dict, List, Optional, Any
from colorama import init, Fore, Style
from fake_useragent import UserAgent
import re

import config

# Initialize colorama
init(autoreset=True)

# Setup logging
logging.basicConfig(
    level=getattr(logging, config.LOG_LEVEL),
    format=config.LOG_FORMAT,
    handlers=[
        logging.FileHandler('tesla_final_bot.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class TeslaFinalBot:
    """Final Tesla bot using exact API format from Tesla's website"""
    
    def __init__(self):
        self.session = requests.Session()
        self.ua = UserAgent()
        self.csrf_token = "MzvIabqJW18Y2asEEKUnGNDLj70TyxYxzUhyXytewSYLAvxYirFqaSm8z2AinBEu5Ki7hCT9LlT/eUdoHW3zFw=="
        self.setup_session()
        
    def setup_session(self):
        """Setup session with exact Tesla headers"""
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Accept": "application/json, text/plain, */*",
            "Accept-Language": "tr-TR,tr;q=0.9,en;q=0.8",
            "Accept-Encoding": "gzip, deflate, br",
            "Connection": "keep-alive",
            "Referer": "https://www.tesla.com/tr_tr/inventory/new/my",
            "Origin": "https://www.tesla.com",
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "same-origin",
            "Content-Type": "application/json",
            "X-Tesla-CSRF-Token": self.csrf_token
        }
        self.session.headers.update(headers)
        
        # Add cookies
        self.session.cookies.update({
            'tesla_locale': 'tr_TR',
            'tesla_market': 'TR'
        })
    
    def build_inventory_query(self) -> Dict[str, Any]:
        """Build exact query format that Tesla expects"""
        return {
            "query": {
                "model": "my",
                "condition": "new",
                "options": {
                    "TRIM": ["MYRWD"]  # Model Y Rear-Wheel Drive (Juniper)
                },
                "market": "TR",
                "language": "tr",
                "arrangeby": "Price",
                "order": "asc",
                "lng": "tr",
                "skipVin": []
            }
        }
    
    def check_inventory(self) -> List[Dict[str, Any]]:
        """Check Tesla inventory"""
        try:
            url = "https://www.tesla.com/coinorder/api/v4/inventory-results"
            query = self.build_inventory_query()
            
            response = self.session.post(
                url,
                json=query,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                results = data.get('results', [])
                
                # Filter for our target vehicles
                available_vehicles = []
                for vehicle in results:
                    if self.is_target_vehicle(vehicle):
                        available_vehicles.append(vehicle)
                
                return available_vehicles
                
            else:
                return []
                
        except Exception as e:
            return []
    
    def is_target_vehicle(self, vehicle: Dict[str, Any]) -> bool:
        """Check if vehicle matches Model Y Juniper criteria"""
        try:
            # Check model
            if vehicle.get('Model') != 'my':
                return False
            
            # Check condition (new)
            if vehicle.get('TitleStatus') != 'new':
                return False
            
            # Check price (max 2M TL)
            price = vehicle.get('Price', 0)
            if price > 2000000:
                return False
            
            # Check for RWD options (Juniper)
            options = vehicle.get('OptionCodeList', [])
            target_options = ['$MTY01', '$MTY08']  # Model Y RWD options
            if not any(opt in options for opt in target_options):
                return False
            
            return True
            
        except Exception as e:
            return False
    
    def display_vehicle_info(self, vehicle: Dict[str, Any]):
        """Display found vehicle information"""
        print(f"\n{Fore.GREEN}🎉 TESLA MODEL Y JUNIPER FOUND! 🎉{Style.RESET_ALL}", flush=True)
        print(f"{Fore.YELLOW}{'='*60}{Style.RESET_ALL}", flush=True)
        
        vin = vehicle.get('VIN', 'N/A')
        price = vehicle.get('Price', 0)
        currency = vehicle.get('CurrencyCode', 'TRY')
        
        print(f"🚗 VIN: {Fore.CYAN}{vin}{Style.RESET_ALL}", flush=True)
        print(f"💰 Price: {Fore.GREEN}{price:,} {currency}{Style.RESET_ALL}", flush=True)
        print(f"📅 Year: {vehicle.get('Year', 'N/A')}", flush=True)
        print(f"🔧 Model: Model Y", flush=True)
        print(f"⚡ Type: Rear-Wheel Drive (Juniper)", flush=True)
        
        # Display options
        options = vehicle.get('OptionCodeList', [])
        if options:
            print(f"🎛️  Options: {', '.join(options)}", flush=True)
        
        # Display availability
        availability = vehicle.get('AvailabilityStatus', 'Unknown')
        print(f"✅ Status: {Fore.GREEN}{availability}{Style.RESET_ALL}", flush=True)
        
        print(f"{Fore.YELLOW}{'='*60}{Style.RESET_ALL}", flush=True)
        print(f"{Fore.RED}🚨 VEHICLE AVAILABLE FOR ORDER! 🚨{Style.RESET_ALL}\n", flush=True)

def main():
    """Main monitoring function"""
    print(f"{Fore.CYAN}🚗 Tesla Model Y Juniper Final Bot{Style.RESET_ALL}", flush=True)
    print(f"{Fore.YELLOW}🎯 Target: Model Y RWD (Juniper) - Max Price: 2,000,000 TRY{Style.RESET_ALL}", flush=True)
    print(f"{Fore.BLUE}⏱️  Checking every {config.CHECK_INTERVAL} seconds{Style.RESET_ALL}", flush=True)
    print(f"{Fore.GREEN}🔧 Using Tesla's exact API format{Style.RESET_ALL}", flush=True)
    print("=" * 60, flush=True)
    
    bot = TeslaFinalBot()
    check_count = 0
    
    try:
        while True:
            check_count += 1
            current_time = datetime.now().strftime("%H:%M:%S")
            
            print(f"{Fore.BLUE}[{current_time}] Check #{check_count:,} - Searching Tesla inventory...{Style.RESET_ALL}", end=" ", flush=True)
            
            vehicles = bot.check_inventory()
            
            if vehicles:
                print(f"{Fore.GREEN}FOUND {len(vehicles)} VEHICLE(S)!{Style.RESET_ALL}", flush=True)
                for vehicle in vehicles:
                    bot.display_vehicle_info(vehicle)
                    
                # Continue monitoring even after finding vehicles
                print(f"{Fore.CYAN}🔄 Continuing to monitor for more vehicles...{Style.RESET_ALL}", flush=True)
            else:
                print(f"{Fore.BLUE}No vehicles found{Style.RESET_ALL}", flush=True)
            
            time.sleep(config.CHECK_INTERVAL)
            
    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}🛑 Bot stopped by user{Style.RESET_ALL}", flush=True)
        print(f"{Fore.CYAN}📊 Total checks performed: {check_count:,}{Style.RESET_ALL}", flush=True)
        print(f"{Fore.GREEN}👋 Thanks for using Tesla Bot!{Style.RESET_ALL}", flush=True)

if __name__ == "__main__":
    main()
